#!/usr/bin/env python3
"""
Test script dla sprawdzenia działania kategorii OLX
"""

from scrapers.olx_scraper import determine_category, determine_relevant_categories, OLX_CATEGORIES

def test_category_detection():
    """Testuje wykrywanie kategorii dla różnych zapytań."""
    
    test_queries = [
        "zegarek męski",
        "laptop gaming",
        "samochód toyota",
        "sukienka letnia", 
        "rower górski",
        "książka programowanie",
        "meble kuchenne",
        "telefon samsung",
        "antyk zegar",
        "pies szczeniak"
    ]
    
    print("=== Test wykrywania kategorii ===")
    for query in test_queries:
        category = determine_category(query)
        relevant_categories = determine_relevant_categories(query)
        
        print(f"\nZapytanie: '{query}'")
        print(f"Główna kategoria: {category if category else 'Brak (wyszukiwanie ogólne)'}")
        print(f"Wszystkie relevantne kategorie: {relevant_categories if relevant_categories else 'Brak'}")
        
        # Sprawdź czy kategoria jest w liście dostępnych kategorii
        if category and category not in OLX_CATEGORIES:
            print(f"BŁĄD: Kategoria {category} nie jest w liście dostępnych kategorii!")

def test_url_construction():
    """Testuje konstrukcję URL-i dla różnych kategorii."""
    
    print("\n=== Test konstrukcji URL-i ===")
    test_cases = [
        ("zegarek męski", "/elektronika/"),
        ("samochód toyota", "/motoryzacja/"),
        ("", ""),  # Brak kategorii
    ]
    
    for query, expected_category in test_cases:
        if expected_category:
            expected_url = f"https://www.olx.pl{expected_category}q-{query}/"
        else:
            expected_url = f"https://www.olx.pl/oferty/q-{query}/"
        
        print(f"Zapytanie: '{query}' -> {expected_url}")

if __name__ == "__main__":
    test_category_detection()
    test_url_construction()
    
    print("\n=== Lista wszystkich dostępnych kategorii ===")
    for i, category in enumerate(OLX_CATEGORIES, 1):
        print(f"{i:2d}. {category}")
