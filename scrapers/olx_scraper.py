import re
import time
import traceback
from playwright.sync_api import Browser, TimeoutError as PlaywrightTimeoutError

# Lista dostępnych kategorii na OLX
OLX_CATEGORIES = [
    "/antyki-i-kolekcje/",
    "/firma-i-przemysl/",
    "/motoryzacja/",
    "/nieruchomosci/",
    "/praca/",
    "/dom-i-ogrod/",
    "/elektronika/",
    "/moda/",
    "/rolnictwo/",
    "/zwierzeta/",
    "/dla-dzieci/",
    "/sport-i-hobby/",
    "/muzyka-i-edukacja/",
    "/zdrowie-i-uroda/",
    "/uslugi/",
    "/noclegi/",
    "/wypozyczalnia/",
    "/oddamo-za-darmo/"
]

# Mapowanie słów kluczowych na kategorie
CATEGORY_KEYWORDS = {
    "/elektronika/": ["telefon", "laptop", "komputer", "tablet", "słuchawki", "telewizor", "konsola", "smartwatch", "zegarek", "elektronika", "sprzęt", "urządzenie"],
    "/motoryzacja/": ["samochód", "auto", "motocykl", "motor", "części", "opony", "felgi", "akumulator", "silnik"],
    "/moda/": ["ubranie", "koszula", "spodnie", "sukienka", "buty", "torebka", "zegarek", "biżuteria", "odzież", "moda"],
    "/dom-i-ogrod/": ["meble", "krzesło", "stół", "szafa", "łóżko", "sofa", "ogród", "narzędzia", "dom"],
    "/sport-i-hobby/": ["rower", "narty", "piłka", "sport", "fitness", "hobby", "gra", "zabawka"],
    "/dla-dzieci/": ["dziecko", "dzieci", "zabawka", "wózek", "fotelik", "ubranka", "buty dziecięce"],
    "/muzyka-i-edukacja/": ["książka", "gitara", "pianino", "instrument", "muzyka", "edukacja", "kurs"],
    "/zdrowie-i-uroda/": ["kosmetyki", "perfumy", "uroda", "zdrowie", "suplementy"],
    "/antyki-i-kolekcje/": ["antyk", "kolekcja", "zabytek", "stary", "vintage", "retro", "zegarek"],
    "/zwierzeta/": ["pies", "kot", "zwierzę", "karma", "akwarium", "klatka"]
}

def determine_category(query: str) -> str:
    """Określa najlepszą kategorię na podstawie zapytania wyszukiwania."""
    query_lower = query.lower()

    # Sprawdź każdą kategorię i jej słowa kluczowe
    for category, keywords in CATEGORY_KEYWORDS.items():
        for keyword in keywords:
            if keyword in query_lower:
                return category

    # Jeśli nie znaleziono dopasowania, zwróć pustą kategorię (wyszukiwanie ogólne)
    return ""

def determine_relevant_categories(query: str) -> list[str]:
    """Określa wszystkie potencjalnie relevantne kategorie dla zapytania."""
    query_lower = query.lower()
    relevant_categories = []

    # Sprawdź każdą kategorię i jej słowa kluczowe
    for category, keywords in CATEGORY_KEYWORDS.items():
        for keyword in keywords:
            if keyword in query_lower:
                if category not in relevant_categories:
                    relevant_categories.append(category)
                break  # Przejdź do następnej kategorii po znalezieniu dopasowania

    return relevant_categories

def search_multiple_categories(browser: Browser, query: str, max_categories: int = 3) -> list[dict]:
    """Wyszukuje oferty w wielu relevantnych kategoriach i łączy wyniki."""
    relevant_categories = determine_relevant_categories(query)

    if not relevant_categories:
        # Jeśli nie znaleziono relevantnych kategorii, wyszukaj ogólnie
        return search(browser, query, category="")

    all_offers = []
    categories_to_search = relevant_categories[:max_categories]  # Ogranicz liczbę kategorii

    for category in categories_to_search:
        print(f"Wyszukuję w kategorii: {category}")
        offers = search(browser, query, category=category)
        all_offers.extend(offers)

    # Usuń duplikaty na podstawie URL
    unique_offers = []
    seen_urls = set()
    for offer in all_offers:
        if offer['url'] not in seen_urls:
            unique_offers.append(offer)
            seen_urls.add(offer['url'])

    return unique_offers

def search(browser: Browser, query: str, category: str = None) -> list[dict]:
    """Wyszukuje oferty na OLX i zwraca listę słowników z detalami."""
    offers = []
    page = browser.new_page()

    try:
        # Automatycznie określ kategorię, jeśli nie została podana
        if category is None:
            category = determine_category(query)

        # Konstruuj URL z kategorią lub bez niej
        if category and category in OLX_CATEGORIES:
            search_url = f"https://www.olx.pl{category}q-{query}/"
            print(f"Wyszukuję na OLX w kategorii {category}: {search_url}")
        else:
            search_url = f"https://www.olx.pl/oferty/q-{query}/"
            print(f"Wyszukuję na OLX (wszystkie kategorie): {search_url}")

        page.goto(search_url, wait_until="domcontentloaded")
        time.sleep(3) # Początkowe oczekiwanie na załadowanie

        # Scrollowanie, aby załadować więcej ofert
        for _ in range(2): # Scrolluj 2 razy, aby załadować więcej wyników
            page.mouse.wheel(0, 10000) # Scrolluj w dół o 10000 pikseli
            time.sleep(2)

        # Selektory dla ofert na OLX - nowa struktura
        # Szukamy kontenerów ofert z tytułami i cenami
        offer_elements = page.locator("div[data-cy='ad-card-title']").all()
        print(f"Znaleziono {len(offer_elements)} potencjalnych ofert na OLX.")

        for offer_element in offer_elements:
            try:
                # Tytuł z h4 wewnątrz kontenera
                title_element = offer_element.locator("h4")
                title = title_element.inner_text().strip()

                # URL z linka w tytule
                link_element = offer_element.locator("a")
                relative_url = link_element.get_attribute("href")
                url = f"https://www.olx.pl{relative_url}" if relative_url and not relative_url.startswith("http") else relative_url

                # Cena z elementu data-testid="ad-price"
                price_element = offer_element.locator("p[data-testid='ad-price']")
                price_text = price_element.inner_text().strip()

                # Wyciągnij liczbę z tekstu ceny (usuń "zł", "do negocjacji" itp.)
                price_match = re.search(r'(\d+(?:[,\s]\d{3})*(?:[,.]\d{2})?)', price_text.replace(' ', ''))
                if price_match:
                    price_str = price_match.group(1).replace(',', '.').replace(' ', '')
                    price = float(price_str)
                else:
                    print(f"Nie udało się sparsować ceny: {price_text}")
                    continue

                if title and price and url:
                    offers.append({"title": title, "price": price, "url": url})
                    print(f"Znaleziono ofertę OLX: {title} - {price} zł")

            except Exception as e:
                # Błąd parsowania pojedynczej oferty, kontynuujemy z następną
                print(f"Błąd podczas parsowania pojedynczej oferty OLX: {e}")
                print(traceback.format_exc())
                continue

    except Exception as e:
        print(f"Wystąpił błąd podczas wyszukiwania na OLX: {e}")
        print(traceback.format_exc())
    finally:
        page.close()
    return offers
